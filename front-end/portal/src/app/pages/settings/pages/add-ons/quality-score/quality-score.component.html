<div *transloco="let t; read: 'pages.settings.add-ons.quality-score'" class="p-4 bg-white rounded-lg shadow">
  <div (click)="toggleOpen()" class="flex items-center space-x-3 cursor-pointer">
    <div class="flex-shrink-0">
      <div class="w-10 h-10 bg-eaglo-blue/15 rounded flex items-center justify-center">
        <i class="fa-regular fa-chart-column text-eaglo-blue"></i>
      </div>
    </div>
    <div class="flex-1">
      <h3 class="text-base font-medium text-gray-900">{{ t('title') }}</h3>
      <p class="text-sm text-gray-500">{{ t('description') }}</p>
    </div>
    <div class="flex-shrink-0">
      <button
        [ngClass]="{
          'rotate-180': open()
        }"
        class="transition ease-in-out"
      >
        <i class="fa-regular fa-chevron-down"></i>
      </button>
    </div>
  </div>

  @if(open()) {
    <div class="pt-4 divide-y divide-gray-200">
      <div class="pb-4">
        @if(guiding()) {

        }

        <div class="flex space-x-4">
          @if(guiding()) {
            <button (click)="toggleGuiding()" class="btn --small --outline">{{ t('buttons.collapse') }}</button>

            <button class="btn w-full --small">
              <i class="fa-regular fa-circle-check"></i>
              <span>{{ t('buttons.add') }}</span>
            </button>
          } @else {
            <button (click)="toggleGuiding()" class="btn w-full --small">
              <i class="fa-regular fa-circle-play"></i>
              <span>{{ t('buttons.start') }}</span>
            </button>
          }
        </div>
      </div>
      @if(response(); as response) {
        @if(response && response.data.length > 0) {
          <div class="pt-4 space-y-4">
            <p>{{ t('connected-accounts') }}</p>
            @for(account of response.data; track $index) {
              <div class="bg-white rounded-lg shadow border border-gray-200 p-4">
                <div class="flex items-center space-x-4">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 rounded flex items-center justify-center">
                      <span class="text-blue-600 font-semibold text-sm">{{ account.name?.charAt(0) }}</span>
                    </div>
                  </div>
                  <div class="flex-1">
                    <h3 class="text-base font-medium text-gray-900">{{ account.name }}</h3>
                    <p class="text-sm text-gray-500 space-x-1">
                      <span>{{ t('account-id') }}</span>
                      <span>{{ account.external_id }}</span>
                    </p>
                  </div>
                </div>
              </div>
            }
          </div>
        }
      }
    </div>
  }
</div>
