import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { TranslocoDirective } from '@jsverse/transloco';
import { JsonPipe, KeyValuePipe, NgClass } from '@angular/common';
import { GoogleAdAccount } from '@api/google/models/google-ad-account.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { GoogleAdAccountService } from '@api/google/services/google-ad-account.service';
import { GoogleScriptType } from '@api/google/enums/google-script-type.enum';
import { catchError, filter, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

enum Step {
  SELECT_ACCOUNT = 'SELECT_ACCOUNT',
  SELECT_DRIVE = 'SELECT_DRIVE',
  CREATING_SHEET = 'CREATING_SHEET',
  ADD_SCRIPT = 'ADD_SCRIPT',
}

@Component({
  selector: 'settings-add-ons-quality-score',
  imports: [TranslocoDirective, NgClass, KeyValuePipe, JsonPipe],
  templateUrl: './quality-score.component.html',
})
export class QualityScoreComponent implements OnInit {
  public response = signal<PaginatedResponse<GoogleAdAccount> | null>(null);
  public open = signal<boolean>(true);
  public loading = signal<boolean>(false);
  public guiding = signal<boolean>(true);
  public activeStep = signal<Step>(Step.SELECT_ACCOUNT);

  public readonly step = Step;

  private googleAdAccountService = inject(GoogleAdAccountService);
  private destroyRef = inject(DestroyRef);

  public ngOnInit(): void {
    this.loadAccounts();
  }

  public toggleOpen(): void {
    this.open.update((value) => !value);
  }

  public toggleGuiding(): void {
    this.guiding.update((value) => !value);
  }

  public loadAccounts(page: number = 1): void {
    if (this.loading()) {
      return;
    }

    this.loading.set(true);

    this.googleAdAccountService
      .index({
        page,
        script_type: GoogleScriptType.QUALITY_SCORE_CHECKER,
      })
      .pipe(
        filter(
          (response): response is PaginatedResponse<GoogleAdAccount> => true,
        ),
        tap((response) => {
          this.response.set(response);
          this.loading.set(false);
        }),
        catchError((err) => {
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
